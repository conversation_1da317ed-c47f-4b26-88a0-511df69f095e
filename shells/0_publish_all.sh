#!/bin/bash

# 顺序执行三个发布脚本
echo "开始执行发布流程..."

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 设置工作目录为项目根目录
SCRIPT_DIR="$(dirname "$0")"
cd "$SCRIPT_DIR/.."

# 显示当前工作目录
echo -e "${GREEN}当前工作目录: $(pwd)${NC}"

# 清空发布目录
LIBS_DIR="./publish_sdk/demo/app/libs"
echo -e "${YELLOW}清空发布目录: $LIBS_DIR${NC}"
if [ -d "$LIBS_DIR" ]; then
    rm -f "$LIBS_DIR"/*.aar
    echo -e "${GREEN}发布目录已清空${NC}"
else
    mkdir -p "$LIBS_DIR"
    echo -e "${GREEN}发布目录已创建${NC}"
fi

# 给 version 版本号+1
echo -e "${YELLOW}更新 SDK 版本号...${NC}"

# 获取版本号文件路径
VERSION_FILE="lib_webview_ad_plugin/src/main/java/ai/ad/webview/plugin/ProxyWSKSDK.java"

# 从文件中提取当前版本号
CURRENT_VERSION=$(grep -o 'private static final String mVersion = "[^"]*"' "$VERSION_FILE" | cut -d'"' -f2)
echo -e "${GREEN}当前SDK版本号: $CURRENT_VERSION${NC}"

# 提取 MAJOR.MINOR.PATCH
IFS='.' read -r MAJOR MINOR PATCH <<< "$CURRENT_VERSION"

# PATCH + 1
NEW_PATCH=$((PATCH))

# 生成新版本号
NEW_VERSION="${MAJOR}.${MINOR}.${NEW_PATCH}"
echo -e "${GREEN}新版本号: $NEW_VERSION${NC}"

# 更新版本号
sed -i '' "s/private static final String mVersion = \"${CURRENT_VERSION}\"/private static final String mVersion = \"${NEW_VERSION}\"/g" "$VERSION_FILE"

echo -e "${GREEN}版本号已更新: $CURRENT_VERSION -> $NEW_VERSION${NC}"

# 处理 todo replace 注释
echo -e "${YELLOW}处理 todo replace 注释...${NC}"

# 遍历两个模块中的所有 Java 和 Kotlin 文件
for module in "lib_webview_ad_sdk" "lib_webview_ad_plugin"; do
    echo -e "${GREEN}处理模块: $module${NC}"

    # 查找所有 Java 和 Kotlin 文件
    find "$module" -type f \( -name "*.java" -o -name "*.kt" \) | while read -r file; do
        # 检查文件是否包含 "// todo replace ->"
        if grep -q "// todo replace ->" "$file"; then
            echo "处理文件: $file"

            # 使用sed直接替换文件内容
            # 将 "// todo replace -> xxx" 替换为 "xxx"
            sed -i '' 's/.*\/\/ todo replace ->\(.*\)/\1/g' "$file"
            echo "  替换完成"
        fi
    done
done

echo -e "${GREEN}todo replace 处理完成${NC}"

# 执行脚本1: 发布DEX SDK
echo "执行脚本1: 发布DEX SDK"
sh "./shells/publish_dex_sdk.sh"
if [ $? -ne 0 ]; then
    echo "脚本1执行失败，退出发布流程"
    exit 1
fi

# 执行脚本2: 发布AAR SDK
echo "执行脚本2: 发布AAR SDK"
sh "./shells/publish_aar_sdk.sh"
if [ $? -ne 0 ]; then
    echo "脚本2执行失败，退出发布流程"
    exit 1
fi

echo "所有发布脚本已成功执行完成！"

# 设置 Demo 目录路径
DEMO_DIR="./publish_sdk/demo"
if [ ! -d "$DEMO_DIR" ]; then
    echo -e "${RED}Demo 目录不存在: $DEMO_DIR${NC}"
    exit 1
fi

# 1. 执行卸载 demo 应用的脚本
echo -e "${YELLOW}1. 正在执行卸载 demo 应用的操作...${NC}"
sh "./shells/uninstall_demo_app.sh"
if [ $? -ne 0 ]; then
    echo -e "${RED}卸载应用失败${NC}"
    # 继续执行，不退出
    echo -e "${YELLOW}继续执行后续步骤...${NC}"
else
    echo -e "${GREEN}卸载应用操作已完成${NC}"
fi

# 2. 使用 build_and_run.sh 脚本编译构建 release 版本的 app 并运行
echo -e "${YELLOW}2. 正在使用 build_and_run.sh 脚本编译构建 release 版本并运行应用...${NC}"
sh "./shells/build_and_run.sh"
if [ $? -ne 0 ]; then
    echo -e "${RED}构建和运行应用失败${NC}"
    exit 1
else
    echo -e "${GREEN}应用已成功构建、安装并启动${NC}"
fi

exit 0