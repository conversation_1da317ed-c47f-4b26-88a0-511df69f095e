package ai.ad.webview.plugin.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.media.MediaDrm;
import android.os.Build;

import java.lang.reflect.Method;
import java.security.MessageDigest;
import java.util.UUID;

import ai.ad.webview.sdk.logger.WSKLog;

public class DeviceIdUtil {
    private static final String TAG = "DeviceIdUtil";
    private static final String PREFS_NAME = "web_resource_device_id";
    private static final String KEY_DEVICE_ID = "deviceId";
    private static final String INVALID_ID_PATTERN = "0000-00";

    // 是否正在预加载设备ID
    private static boolean isPreloading = false;
    private static String mDeviceID = "";

    private DeviceIdUtil() {
        // 私有构造函数，防止实例化
    }

    /**
     * 在SDK初始化时预加载设备ID
     * 按照GAID -> MediaDRM -> UUID的顺序尝试获取
     */
    public static void preloadGAID(final Context context) {
        // 如果已经在预加载中，则不重复预加载
        if (isPreloading) {
            WSKLog.d(TAG, "Device ID is being preloaded");
            return;
        }

        // 检查是否已有缓存的设备ID
        try {
            SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
            String cachedId = prefs.getString(KEY_DEVICE_ID, null);
            if (cachedId != null && !cachedId.trim().isEmpty()) {
                WSKLog.d(TAG, "Cached device ID already exists: " + cachedId + ", no need to preload");
                mDeviceID = cachedId;
                return;
            }
        } catch (Exception e) {
            WSKLog.e(TAG, "Failed to read cached device ID: " + e.getMessage());
            // 继续执行，尝试获取新的设备ID
        }

        isPreloading = true;
        WSKLog.d(TAG, "Starting to preload device ID");

        // 在单独的线程中获取设备ID
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    String deviceId = null;

                    // 1. 尝试获取GAID
                    try {
                        WSKLog.d(TAG, "Attempting to get GAID...");
                        // 使用反射调用AdvertisingIdClient，避免直接引用可能不存在的类
                        Class<?> advertisingIdClientClass =
                                Class.forName("com.google.android.gms.ads.identifier.AdvertisingIdClient");
                        Method getAdvertisingIdInfoMethod = advertisingIdClientClass.getMethod(
                                "getAdvertisingIdInfo",
                                Context.class
                        );
                        Object adInfo = getAdvertisingIdInfoMethod.invoke(null, context);

                        if (adInfo != null) {
                            // 通过反射获取id属性
                            Method idMethod = adInfo.getClass().getMethod("getId");
                            String id = (String) idMethod.invoke(adInfo);

                            if (id != null && !id.trim().isEmpty() && !id.contains(INVALID_ID_PATTERN)) {
                                deviceId = id;
                                WSKLog.d(TAG, "Successfully obtained GAID: " + id);
                            } else {
                                WSKLog.d(TAG, "Obtained invalid GAID: " + id);
                            }
                        } else {
                            WSKLog.d(TAG, "Getting GAID returned null");
                        }
                    } catch (ClassNotFoundException e) {
                        WSKLog.e(TAG, "Cannot find AdvertisingIdClient class: " + e.getMessage());
                    } catch (Exception e) {
                        WSKLog.e(TAG, "Failed to get GAID: " + e.getMessage());
                    }

                    // 2. 如果GAID获取失败，尝试获取MediaDRM ID
                    if (deviceId == null) {
                        try {
                            WSKLog.d(TAG, "Attempting to get MediaDRM ID...");
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
                                UUID wideVineUuid = new UUID(-0x121074568629b532L, -0x5c37d8232ae2de13L);
                                MediaDrm mediaDrm = new MediaDrm(wideVineUuid);
                                byte[] mediaId = mediaDrm.getPropertyByteArray(MediaDrm.PROPERTY_DEVICE_UNIQUE_ID);

                                if (mediaId != null && mediaId.length > 0) {
                                    // 将字节数组转换为十六进制字符串
                                    MessageDigest md5 = MessageDigest.getInstance("MD5");
                                    byte[] digest = md5.digest(mediaId);
                                    StringBuilder hexString = new StringBuilder();
                                    for (byte b : digest) {
                                        hexString.append(String.format("%02x", b));
                                    }

                                    if (!hexString.toString().contains(INVALID_ID_PATTERN)) {
                                        deviceId = hexString.toString();
                                        WSKLog.d(TAG, "Successfully obtained MediaDRM ID: " + hexString);
                                    } else {
                                        WSKLog.d(TAG, "Obtained invalid MediaDRM ID: " + hexString);
                                    }
                                } else {
                                    WSKLog.d(TAG, "MediaDRM ID is empty");
                                }
                            } else {
                                WSKLog.d(TAG, "Android version too low, cannot get MediaDRM ID");
                            }
                        } catch (Exception e) {
                            WSKLog.e(TAG, "Failed to get MediaDRM ID: " + e.getMessage());
                        }
                    }

                    // 3. 如果前两种方法都失败，生成UUID
                    if (deviceId == null) {
                        deviceId = UUID.randomUUID().toString();
                        WSKLog.d(TAG, "Generated random UUID as device ID: " + deviceId);
                    }

                    // 缓存获取到的设备ID
                    try {
                        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
                        SharedPreferences.Editor editor = prefs.edit();
                        editor.putString(KEY_DEVICE_ID, deviceId);
                        editor.apply();
                        mDeviceID = deviceId;
                        WSKLog.d(TAG, "Device ID preloading completed, cached: " + deviceId);
                    } catch (Exception e) {
                        WSKLog.e(TAG, "Failed to cache device ID: " + e.getMessage());
                        // 即使缓存失败，也设置内存中的设备ID
                        mDeviceID = deviceId;
                    }

                } catch (Exception e) {
                    WSKLog.e(TAG, "Exception occurred during device ID preloading: " + e.getMessage());
                    // 如果整个过程失败，生成一个随机UUID作为备用
                    try {
                        mDeviceID = UUID.randomUUID().toString();
                        WSKLog.d(TAG, "Preloading failed, using backup random UUID: " + mDeviceID);
                    } catch (Exception ex) {
                        WSKLog.e(TAG, "Failed to generate backup UUID: " + ex.getMessage());
                    }
                } finally {
                    isPreloading = false;
                }
            }
        }).start();
    }

    /**
     * 获取设备ID:
     * 1. 从缓存获取设备ID
     * 2. 如果缓存中没有，返回空字符串
     */
    public static String getDeviceId() {
        // todo by shawn
        return "fsadfasdf";
//        if (mDeviceID == null || mDeviceID.isEmpty()) {
//            return "";
//        }
//        return mDeviceID;
    }

    /**
     * 检查设备ID是否已获取（不为空）
     *
     * @return 如果设备ID不为空，返回true；否则返回false
     */
    public static boolean isDeviceIdReady() {
        return mDeviceID != null && !mDeviceID.isEmpty();
    }
}
