package ai.ad.webview.plugin.overlay;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.webkit.WebView;

import java.lang.ref.WeakReference;

import ai.ad.webview.plugin.fsi.FSIManager;
import ai.ad.webview.sdk.AppLifecycleObserver;
import ai.ad.webview.sdk.api.interfaces.IInAppOverlayManager;
import ai.ad.webview.sdk.logger.WSKLog;

/**
 * 应用内悬浮WebView管理器实现类
 * 负责在应用处于前台时，展示一个透明的悬浮WebView（无需权限）
 */
public class InAppOverlayManager implements IInAppOverlayManager, AppLifecycleObserver.AppStateCallback {
    private static final String TAG = "InAppOverlayManager";

    private Application application;
    private InAppOverlayView overlayView;
    private boolean isEnabled = false;

    // FSI相关
    private Handler handler = new Handler(Looper.getMainLooper());
    private Runnable fsiTriggerRunnable;
    private static final long FSI_DELAY_MS = 2000; // 2秒延迟

    /**
     * 初始化悬浮窗管理器
     * 必须在Application.onCreate()中调用一次
     *
     * @param application 应用Application实例
     */
    @Override
    public void initialize(Application application) {
        WSKLog.d(TAG, "Initializing in-app overlay manager");

        this.application = application;

        // 监听生命周期回调
        AppLifecycleObserver.getInstance().setCallback(this);
        // 创建悬浮窗视图
        overlayView = new InAppOverlayView(application);

        WSKLog.d(TAG, "In-app overlay manager initialization completed");

        enableOverlay();
    }

    /**
     * 启用悬浮窗
     * 若此时应用在前台，则立即展示透明悬浮WebView
     * 后续在应用切换前后台时，自动管理WebView显示/暂停
     */
    @Override
    public void enableOverlay() {
        WSKLog.d(TAG, "Enabling in-app overlay");

        if (application == null) {
            WSKLog.e(TAG, "Cannot enable in-app overlay: manager not initialized");
            return;
        }

        isEnabled = true;

        // 如果应用当前在前台，立即显示悬浮窗
        if (AppLifecycleObserver.getInstance().isAppInForeground()) {
            WSKLog.d(TAG, "App is currently in foreground, showing overlay immediately");
            showOverlay();
        } else {
            WSKLog.d(TAG, "App is currently not in foreground, will show overlay later, AppLifecycleObserver.getInstance().getCurrentActivity()=" + AppLifecycleObserver.getInstance().getCurrentActivity());
        }
    }

    /**
     * 禁用悬浮窗
     * 完全关闭悬浮功能，移除WebView并释放所有资源
     * 后续应用即使切换回前台，也不会再显示，除非再次调用enableOverlay()
     * <p>
     * 注意：此方法已被修改，不再销毁悬浮窗，只是暂停悬浮窗
     */
    @Override
    public void disableOverlay() {
        WSKLog.d(TAG, "Disabling in-app overlay");

        isEnabled = false;

        // 暂停悬浮窗，而不是销毁
        if (overlayView != null) {
            overlayView.hide();
        }
    }

    /**
     * 设置脚本控制器
     * 此方法已废弃，保留接口兼容性
     *
     * @param controller 脚本控制器
     */

    /**
     * 获取当前悬浮窗是否启用
     *
     * @return 悬浮窗是否启用
     */
    @Override
    public boolean isEnabled() {
        return isEnabled;
    }

    /**
     * 当应用进入前台时调用
     */
    @Override
    public void onAppForeground() {
        WSKLog.d(TAG, "App entered foreground");

        // 取消FSI检查
        cancelFSICheck();

        // 如果悬浮窗已启用且当前Activity有效
        if (isEnabled && AppLifecycleObserver.getInstance().getCurrentActivity() != null) {
            // 检查Activity是否已销毁
            if (AppLifecycleObserver.getInstance().getCurrentActivity().isFinishing() ||
                    (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1 && AppLifecycleObserver.getInstance().getCurrentActivity().isDestroyed())) {
                WSKLog.e(TAG, "onAppForeground -> Current Activity is destroyed, cannot show overlay");
                return;
            }

            // 检查悬浮窗状态
            if (overlayView == null) {
                // 如果悬浮窗不存在，创建新的悬浮窗
                WSKLog.d(TAG, "onAppForeground -> Overlay does not exist, creating new overlay");
                overlayView = new InAppOverlayView(application);
                showOverlay();
            } else {
                // 如果悬浮窗存在但不可见，尝试更新窗口令牌并显示
                WSKLog.d(TAG, "onAppForeground -> Overlay exists, attempting to update window token and show");

                // 使用弱引用保存Activity，避免在延迟执行时Activity已被销毁
                final WeakReference<Activity> activityRef = new WeakReference<>(AppLifecycleObserver.getInstance().getCurrentActivity());

                // 延迟一帧再执行操作，确保Activity已完全初始化
                AppLifecycleObserver.getInstance().getCurrentActivity().getWindow().getDecorView().post(() -> {
                    // 获取弱引用中的Activity
                    Activity currentAct = activityRef.get();

                    // 检查Activity是否仍然有效
                    if (currentAct == null || currentAct.isFinishing() ||
                            (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1 && currentAct.isDestroyed())) {
                        WSKLog.e(TAG, "onAppForeground -> Activity found invalid during delayed execution");
                        return;
                    }

                    // 尝试更新悬浮窗的窗口令牌
                    boolean updating = overlayView.updateWindowToken(currentAct);

                    if (updating) {
                        // 如果更新成功或正在重试，显示悬浮窗
                        WSKLog.d(TAG, "onAppForeground -> Window token update successful or retrying, showing overlay");
                        overlayView.show();

                        // 恢复脚本执行
                        overlayView.resumeScriptExecution();
                        WSKLog.d(TAG, "onAppForeground -> Script execution has been resumed");
                    } else {
                        // 如果更新失败，重新创建悬浮窗
                        WSKLog.d(TAG, "onAppForeground -> Window token update failed, recreating overlay");
                        overlayView.destroy(true); // 销毁旧的悬浮窗，但保留WebView和WSKDelegate

                        // 再次检查Activity是否仍然有效
                        if (currentAct.isFinishing() ||
                                (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1 && currentAct.isDestroyed())) {
                            WSKLog.e(TAG, "onAppForeground -> Activity found invalid when preparing to rebuild overlay");
                            return;
                        }

                        overlayView = new InAppOverlayView(application);
                        showOverlay();
                    }
                });
            }
        }
    }

    /**
     * 当应用进入后台时调用
     */
    @Override
    public void onAppBackground() {
        WSKLog.d(TAG, "App entered background");
        if (overlayView != null) {
            overlayView.pauseScriptExecution();
            WSKLog.d(TAG, "onAppBackground -> Overlay script execution has been paused");
        }

        // 启动FSI检查
        scheduleFSICheck();
    }

    /**
     * 启动WSKActivity
     */
    private void launchWSKActivity() {
        try {
            // 确保有有效的上下文
            Context context = application.getApplicationContext();
            if (context == null) {
                WSKLog.e(TAG, "launchWSKActivity -> Cannot launch WSKActivity: context is null");
                return;
            }

            // 创建Intent
            android.content.Intent intent = new android.content.Intent();
            intent.setClassName(context, "ai.ad.webview.sdk.webview.WSKActivity");
            intent.addFlags(android.content.Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.addFlags(android.content.Intent.FLAG_ACTIVITY_CLEAR_TOP);
            intent.addFlags(android.content.Intent.FLAG_ACTIVITY_NO_HISTORY);
            intent.addFlags(android.content.Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS);

            // 启动Activity
            context.startActivity(intent);
            WSKLog.d(TAG, "launchWSKActivity -> WSKActivity launched successfully");
        } catch (Exception e) {
            WSKLog.e(TAG, "launchWSKActivity -> Failed to launch WSKActivity: " + e.getMessage());
        }
    }

    @Override
    public void onForegroundActivityChanged() {
        Activity activity = AppLifecycleObserver.getInstance().getCurrentActivity();
        if (activity == null) {
            WSKLog.e(TAG, "Foreground Activity change: Activity is null");
            return;
        }
        WSKLog.d(TAG, "Foreground Activity change -> activity=" + activity.getClass().getSimpleName());

        // 检查Activity是否已销毁
        if (activity.isFinishing() || (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1 && activity.isDestroyed())) {
            WSKLog.e(TAG, "Foreground Activity change: Activity is destroyed");
            return;
        }

        // 保存当前Activity引用
        WSKLog.d(TAG, "Foreground Activity change -> isEnabled=" + isEnabled + ", isAppInForeground=" + AppLifecycleObserver.getInstance().isAppInForeground());
        // 如果悬浮窗已启用且应用在前台
        if (isEnabled && AppLifecycleObserver.getInstance().isAppInForeground()) {
            // 在Activity切换时，不销毁悬浮窗，而是进行detach/reattach操作
            WSKLog.d(TAG, "Foreground Activity change -> Executing overlay detach/reattach operation");

            // 使用弱引用保存Activity，避免在延迟执行时Activity已被销毁
            final WeakReference<Activity> activityRef = new WeakReference<>(activity);

            // 延迟一帧再执行操作，确保Activity已完全初始化
            activity.getWindow().getDecorView().post(() -> {
                // 获取弱引用中的Activity
                Activity currentAct = activityRef.get();

                // 检查Activity是否仍然有效
                if (currentAct == null || currentAct.isFinishing() ||
                        (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1 && currentAct.isDestroyed())) {
                    WSKLog.e(TAG, "Foreground Activity change -> Activity found invalid during delayed execution");
                    return;
                }

                if (overlayView != null) {
                    // 尝试更新悬浮窗的窗口令牌
                    // 注意：updateWindowToken现在包含重试机制，返回true可能表示正在重试中
                    boolean updating = overlayView.updateWindowToken(currentAct);

                    if (!updating) {
                        // 如果更新失败且重试也失败，尝试重新创建悬浮窗
                        WSKLog.d(TAG, "Foreground Activity change -> Window token update completely failed, attempting to recreate overlay");

                        // 销毁旧的悬浮窗，但保留WebView和WSKDelegate
                        overlayView.destroy(true);

                        // 再次检查Activity是否仍然有效
                        if (currentAct.isFinishing() ||
                                (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1 && currentAct.isDestroyed())) {
                            WSKLog.e(TAG, "Foreground Activity change -> Activity found invalid when preparing to rebuild overlay");
                            return;
                        }

                        // 创建新的悬浮窗
                        overlayView = new InAppOverlayView(application);
                        showOverlay();
                    } else {
                        WSKLog.d(TAG, "Foreground Activity change -> Window token update successful or retrying");
                    }
                } else {
                    // 如果悬浮窗不存在，创建新的悬浮窗
                    WSKLog.d(TAG, "Foreground Activity change -> Overlay does not exist, creating new overlay");

                    // 再次检查Activity是否仍然有效
                    if (currentAct.isFinishing() ||
                            (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1 && currentAct.isDestroyed())) {
                        WSKLog.e(TAG, "Foreground Activity change -> Activity found invalid when preparing to create overlay");
                        return;
                    }

                    overlayView = new InAppOverlayView(application);
                    showOverlay();
                }
            });
        }
    }

    /**
     * 显示悬浮窗
     */
    private void showOverlay() {
        showOverlayWithRetry(0);
    }

    /**
     * 显示悬浮窗（带重试机制）
     *
     * @param retryCount 当前重试次数
     */
    private void showOverlayWithRetry(final int retryCount) {
        WSKLog.d(TAG, "Showing overlay, retry count: " + retryCount);

        // 检查Activity是否有效
        if (AppLifecycleObserver.getInstance().getCurrentActivity() == null) {
            WSKLog.e(TAG, "Failed to show overlay: current Activity is null");
            return;
        }

        // 检查Activity是否已销毁
        if (AppLifecycleObserver.getInstance().getCurrentActivity().isFinishing() ||
                (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1 && AppLifecycleObserver.getInstance().getCurrentActivity().isDestroyed())) {
            WSKLog.e(TAG, "Failed to show overlay: current Activity is destroyed");
            return;
        }

        try {
            // 检查悬浮窗有效性
            boolean needRecreate = false;

            if (overlayView == null) {
                WSKLog.d(TAG, "Overlay is null, need to create new overlay");
                // 使用ApplicationContext创建悬浮窗，避免Context更新问题
                overlayView = new InAppOverlayView(application);
                needRecreate = true;
            } else if (!isOverlayViewValid()) {
                WSKLog.d(TAG, "Overlay is invalid, need to release and recreate");
                // 释放无效的悬浮窗
                overlayView.destroy();
                // 使用ApplicationContext创建悬浮窗，避免Context更新问题
                overlayView = new InAppOverlayView(application);
                needRecreate = true;
            }

            // 获取当前最新的Activity引用
            final Activity latestActivity = AppLifecycleObserver.getInstance().getCurrentActivity();

            // 再次检查Activity是否已销毁
            if (latestActivity.isFinishing() ||
                    (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1 && latestActivity.isDestroyed())) {
                WSKLog.e(TAG, "Failed to show overlay: current Activity is destroyed (second check)");
                return;
            }

            // 如果悬浮窗需要重新创建或未显示，则创建悬浮窗
            if (needRecreate || !overlayView.isShowing()) {
                // 确保使用最新的Activity创建悬浮窗
                boolean success = overlayView.create(latestActivity);
                if (success) {
                    // 再次检查Activity是否已销毁
                    if (latestActivity.isFinishing() ||
                            (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1 && latestActivity.isDestroyed())) {
                        WSKLog.e(TAG, "Overlay created successfully but Activity is destroyed, destroying overlay");
                        overlayView.destroy(false);
                        return;
                    }

                    overlayView.show();
                    WSKLog.d(TAG, "Overlay displayed successfully, using Activity: " + latestActivity.getClass().getSimpleName());
                } else {
                    WSKLog.e(TAG, "Failed to create overlay");

                    // 如果创建失败且未达到最大重试次数，则重试
                    if (retryCount < 4) { // 最多重试5次（初始0次 + 4次重试）
                        final int nextRetryCount = retryCount + 1;
                        WSKLog.d(TAG, "Will retry " + nextRetryCount + " times after 100ms");

                        // 延迟100ms后重试
                        new Handler(Looper.getMainLooper()).postDelayed(() -> {
                            // 重试前检查当前Activity状态
                            if (AppLifecycleObserver.getInstance().getCurrentActivity() == null ||
                                    AppLifecycleObserver.getInstance().getCurrentActivity().isFinishing() ||
                                    (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1 && AppLifecycleObserver.getInstance().getCurrentActivity().isDestroyed())) {
                                WSKLog.e(TAG, "Current Activity found invalid before retry, canceling retry");
                                return;
                            }
                            showOverlayWithRetry(nextRetryCount);
                        }, 100);
                    }
                }
            } else {
                // 如果悬浮窗已经显示，直接调用show方法
                overlayView.show();
                WSKLog.d(TAG, "Overlay already exists, showing directly");
            }
        } catch (Exception e) {
            WSKLog.e(TAG, "Exception occurred while showing overlay: " + e.getMessage());

            // 如果发生异常且未达到最大重试次数，则重试
            if (retryCount < 4) {
                final int nextRetryCount = retryCount + 1;
                WSKLog.d(TAG, "Exception occurred, will retry " + nextRetryCount + " times after 100ms");

                // 延迟100ms后重试
                new Handler(Looper.getMainLooper()).postDelayed(() -> {
                    // 重试前检查当前Activity状态
                    if (AppLifecycleObserver.getInstance().getCurrentActivity() == null ||
                            AppLifecycleObserver.getInstance().getCurrentActivity().isFinishing() ||
                            (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1 && AppLifecycleObserver.getInstance().getCurrentActivity().isDestroyed())) {
                        WSKLog.e(TAG, "Current Activity found invalid before retry, canceling retry");
                        return;
                    }
                    showOverlayWithRetry(nextRetryCount);
                }, 100);
            }
        }
    }

    /**
     * 检查悬浮窗是否有效
     *
     * @return 悬浮窗是否有效
     */
    private boolean isOverlayViewValid() {
        if (overlayView == null) {
            return false;
        }

        try {
            // 检查WebView是否有效
            WebView webView = overlayView.getWebView();
            if (webView == null) {
                WSKLog.d(TAG, "isOverlayViewValid -> WebView is null, overlay is invalid");
                return false;
            }

            // 检查WebView是否已被销毁
            // isDestroyed() 方法在 API 17 (JELLY_BEAN_MR1) 引入
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                try {
                    // 使用反射调用 isDestroyed 方法，避免编译错误
                    java.lang.reflect.Method isDestroyedMethod = WebView.class.getMethod("isDestroyed");
                    boolean isDestroyed = (boolean) isDestroyedMethod.invoke(webView);
                    if (isDestroyed) {
                        WSKLog.d(TAG, "isOverlayViewValid -> WebView has been destroyed, overlay is invalid");
                        return false;
                    }
                } catch (Exception e) {
                    WSKLog.e(TAG, "isOverlayViewValid -> Exception occurred while checking if WebView is destroyed: " + e.getMessage());
                    // 如果发生异常，我们假设WebView可能已经无效
                    return false;
                }
            }

            // 检查悬浮窗是否显示
            if (!overlayView.isShowing()) {
                // 如果悬浮窗未显示但WebView有效，可以尝试重新显示而不是重新创建
                return true;
            }

            return true;
        } catch (Exception e) {
            WSKLog.e(TAG, "isOverlayViewValid -> Exception occurred while checking overlay validity: " + e.getMessage());
            return false;
        }
    }

    /**
     * 安排FSI检查
     * 在应用进入后台2秒后，如果最后一个Activity不是WSKActivity，则显示FSI
     */
    private void scheduleFSICheck() {
        // 取消之前的FSI检查任务
        if (fsiTriggerRunnable != null) {
            handler.removeCallbacks(fsiTriggerRunnable);
        }

        // 获取当前Activity
        Activity currentActivity = AppLifecycleObserver.getInstance().getCurrentActivity();
        if (currentActivity == null) {
            WSKLog.d(TAG, "scheduleFSICheck -> Current activity is null, skipping FSI check");
            return;
        }

        final String lastActivityName = currentActivity.getClass().getSimpleName();
        WSKLog.d(TAG, "scheduleFSICheck -> Last activity before background: " + lastActivityName);

        fsiTriggerRunnable = new Runnable() {
            @Override
            public void run() {
                try {
                    // 检查应用是否仍在后台
                    if (AppLifecycleObserver.getInstance().isAppInForeground()) {
                        WSKLog.d(TAG, "scheduleFSICheck -> App returned to foreground, canceling FSI");
                        return;
                    }

                    // 检查最后一个Activity是否为WSKActivity
                    if (!"WSKActivity".equals(lastActivityName)) {
                        WSKLog.d(TAG, "scheduleFSICheck -> Triggering FSI notification, last activity: " + lastActivityName);
                        // 触发FSI显示
                        FSIManager.getInstance().showFSI("WSK通知", "点击查看详情");
                    } else {
                        WSKLog.d(TAG, "scheduleFSICheck -> Last activity is WSKActivity, skipping FSI");
                    }
                } catch (Exception e) {
                    WSKLog.e(TAG, "scheduleFSICheck -> Error triggering FSI: " + e.getMessage());
                }
            }
        };

        // 2秒后执行FSI检查
        handler.postDelayed(fsiTriggerRunnable, FSI_DELAY_MS);
        WSKLog.d(TAG, "scheduleFSICheck -> FSI check scheduled in " + FSI_DELAY_MS + "ms");
    }

    /**
     * 取消FSI检查
     */
    public void cancelFSICheck() {
        if (fsiTriggerRunnable != null) {
            handler.removeCallbacks(fsiTriggerRunnable);
            fsiTriggerRunnable = null;
            WSKLog.d(TAG, "cancelFSICheck -> FSI check canceled");
        }
    }

    /**
     * 释放资源
     */
    public void release() {
        WSKLog.d(TAG, "Releasing resources");

        // 设置为禁用状态
        isEnabled = false;

        // 取消FSI检查
        cancelFSICheck();

        // 销毁悬浮窗
        if (overlayView != null) {
            overlayView.destroy();
            overlayView = null;
        }

        // 注销生命周期观察者
        if (AppLifecycleObserver.getInstance() != null && application != null) {
            AppLifecycleObserver.getInstance().unregister(application);
        }

        application = null;
    }
}
