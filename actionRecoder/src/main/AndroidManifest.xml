<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />

    <application
        android:name="ai.ad.webview.plugin.RecorderApp"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="WebView Recorder"
        android:supportsRtl="true"
        android:theme="@style/Theme.ActionRecorder"
        android:requestLegacyExternalStorage="true">
        <activity
            android:name="ai.ad.webview.plugin.actionrecoder.WSKActionRecordActivity"
            android:exported="true"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:windowSoftInputMode="adjustResize"
            android:theme="@style/Theme.ActionRecorder">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="ai.ad.webview.plugin.actionrecoder.ScriptListActivity"
            android:exported="false" />
        <activity
            android:name="ai.ad.webview.plugin.actionrecoder.TestActivity"
            android:exported="false" />
        <activity
            android:name="ai.ad.webview.plugin.actionrecoder.WebViewPlaybackActivity"
            android:configChanges="orientation|screenSize"
            android:exported="false"
            android:screenOrientation="portrait" />

        <!-- 添加 FileProvider 配置 -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="ai.ad.webview.sdk.actionrecoder.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
    </application>

</manifest>