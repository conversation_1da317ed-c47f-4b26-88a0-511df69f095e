# FSI 全屏通知修复测试指南

## 修复内容总结

### 1. FSIManager.java 修改
- **移除非锁屏状态限制**：不再在非锁屏状态下使用空PendingIntent
- **始终使用全屏Intent**：无论锁屏状态如何，都设置全屏Intent
- **直接启动Activity**：在非锁屏状态下额外调用startActivity确保显示

### 2. FULLWSKActivity.java 修改
- **添加锁屏显示方法**：turnScreenOnAndKeyguardOff()方法
- **增强窗口标志**：添加更多窗口标志确保前台显示
- **兼容性处理**：支持Android 8.1前后的不同API

### 3. AndroidManifest.xml 修改
- **Activity属性**：添加showOnLockScreen、turnScreenOn、showWhenLocked
- **新增权限**：SYSTEM_ALERT_WINDOW、FOREGROUND_SERVICE

## 测试步骤

### 测试1：非锁屏状态下的FSI通知
1. 确保设备处于非锁屏状态（屏幕亮着且已解锁）
2. 打开FSI Tester应用
3. 点击"测试前台FSI"按钮
4. **预期结果**：应该立即弹出全屏Activity显示百度页面

### 测试2：锁屏状态下的FSI通知
1. 点击"测试锁屏FSI"按钮（会自动锁屏并发送通知）
2. **预期结果**：锁屏界面应显示全屏Activity

### 测试3：权限检查
1. 点击"检查权限"按钮
2. 确保所有必要权限已授予
3. 特别检查"显示在其他应用上层"权限

## 关键修复点

### 问题根因
原代码在非锁屏状态下使用createEmptyPendingIntent()，导致无法触发全屏Activity。

### 解决方案
1. **移除限制逻辑**：不再区分锁屏/非锁屏状态
2. **双重保障**：通知+直接启动Activity
3. **增强权限**：添加必要的系统级权限
4. **窗口优化**：使用更强的窗口标志

## 技术细节

### 核心修改代码
```java
// FSIManager.java - 始终使用全屏Intent
builder.setFullScreenIntent(fullScreenPendingIntent, true);

// 非锁屏状态额外启动Activity
if (!isLocked) {
    fullScreenIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | 
                            Intent.FLAG_ACTIVITY_CLEAR_TOP |
                            Intent.FLAG_ACTIVITY_SINGLE_TOP |
                            Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT);
    context.startActivity(fullScreenIntent);
}
```

### 窗口标志优化
```java
// FULLWSKActivity.java - 增强窗口显示
window.addFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
        | WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
        | WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH);
```

## 注意事项

1. **权限要求**：需要用户手动授予"显示在其他应用上层"权限
2. **系统限制**：Android 10+对后台启动Activity有限制，但全屏Intent不受影响
3. **设备差异**：不同厂商的设备可能有不同的行为
4. **电池优化**：确保应用未被系统电池优化限制

## 验证方法

运行测试后，检查日志输出：
- 查找"非锁屏状态，直接启动全屏Activity"日志
- 确认Activity成功启动且显示在前台
- 验证WebView正常加载百度页面
