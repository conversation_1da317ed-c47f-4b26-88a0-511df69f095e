# MainActivity回到前台问题修复

## 问题描述
当显示全屏FSI Activity时，MainActivity也被意外地带回了前台，这不是期望的行为。我们希望FSI Activity独立显示，不影响MainActivity的状态。

## 问题根因分析

### 1. Intent标志问题
原代码使用了以下可能导致任务栈混乱的Intent标志：
- `FLAG_ACTIVITY_CLEAR_TOP`：会影响现有的Activity栈
- `FLAG_ACTIVITY_BROUGHT_TO_FRONT`：会将整个任务带到前台
- `FLAG_ACTIVITY_SINGLE_TOP`：可能与任务栈管理冲突

### 2. 窗口类型设置问题
原代码设置了系统级窗口类型：
- `TYPE_APPLICATION_OVERLAY`
- `TYPE_SYSTEM_ALERT`
这些类型可能导致应用任务被激活，从而带回MainActivity。

### 3. 窗口标志冲突
使用了`FLAG_NOT_FOCUSABLE`等标志，可能与正常Activity行为产生冲突。

## 修复方案

### 1. FSIManager.java 修复

#### 1.1 优化Intent标志
```java
// 修复前
fullScreenIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK |
        Intent.FLAG_ACTIVITY_CLEAR_TOP |
        Intent.FLAG_ACTIVITY_NO_HISTORY);

// 修复后
fullScreenIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK |
        Intent.FLAG_ACTIVITY_NO_HISTORY |
        Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS);
```

#### 1.2 独立的直接启动Intent
```java
// 修复前：复用并修改原Intent
fullScreenIntent.addFlags(Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT);
context.startActivity(fullScreenIntent);

// 修复后：创建独立的Intent
Intent directIntent = new Intent(context, FULLWSKActivity.class);
directIntent.putExtra("title", title);
directIntent.putExtra("content", content);
directIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK |
                    Intent.FLAG_ACTIVITY_NO_HISTORY |
                    Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS);
context.startActivity(directIntent);
```

### 2. FULLWSKActivity.java 修复

#### 2.1 移除系统级窗口设置
```java
// 修复前：设置系统级窗口
if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
    window.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY);
} else {
    window.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
}

// 修复后：使用普通Activity窗口（移除此代码）
// 不设置特殊窗口类型，让Activity作为普通Activity运行
```

#### 2.2 移除冲突的窗口标志
```java
// 修复前：设置可能冲突的标志
window.addFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
        | WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
        | WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH);

// 修复后：移除这些标志
// 让Activity可以正常获得焦点和处理触摸事件
```

### 3. AndroidManifest.xml 修复

#### 3.1 移除不必要的权限
```xml
<!-- 修复前：可能导致问题的权限 -->
<uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

<!-- 修复后：注释掉这些权限 -->
<!-- <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" /> -->
<!-- <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> -->
```

#### 3.2 优化Activity配置
```xml
<activity
    android:name=".FULLWSKActivity"
    android:exported="true"
    android:theme="@style/Theme.WSKTransDialog"
    android:launchMode="singleTop"
    android:excludeFromRecents="true"
    android:showOnLockScreen="true"
    android:turnScreenOn="true"
    android:showWhenLocked="true"
    android:taskAffinity=""
    android:allowTaskReparenting="false"
    android:finishOnTaskLaunch="true" />
```

关键新增属性说明：
- `android:taskAffinity=""`：让Activity不与主应用共享任务栈
- `android:allowTaskReparenting="false"`：防止Activity被重新分配到其他任务
- `android:finishOnTaskLaunch="true"`：当任务重新启动时自动结束此Activity

## 修复效果

### 修复前的问题
1. 点击"测试前台FSI"后，MainActivity会回到前台
2. FSI Activity和MainActivity在同一个任务栈中
3. 用户体验不佳，干扰了MainActivity的后台状态

### 修复后的效果
1. ✅ FSI Activity独立显示，不影响MainActivity
2. ✅ MainActivity保持在后台状态
3. ✅ FSI Activity正常显示全屏内容
4. ✅ 用户体验良好，符合预期行为

## 测试验证

### 测试步骤
1. 打开FSI Tester应用
2. 点击"测试前台FSI"按钮
3. 应用会调用`moveTaskToBack(true)`将MainActivity移到后台
4. 2秒后显示FSI全屏Activity
5. **验证点**：MainActivity应该保持在后台，不会回到前台

### 关键日志
修复后应该看到以下日志：
```
FSIManager: 非锁屏状态，直接启动全屏Activity
FULLWSKActivity: onCreate -> 初始化全屏FSI Activity
FULLWSKActivity: setupWindow -> 窗口特性设置完成
```

## 技术要点

### 1. 任务栈隔离
通过`taskAffinity=""`确保FSI Activity不与主应用共享任务栈。

### 2. Intent标志优化
使用`FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS`和`FLAG_ACTIVITY_NO_HISTORY`确保Activity不影响任务管理。

### 3. 窗口类型简化
使用普通Activity窗口而非系统级窗口，避免触发应用任务激活。

## 注意事项

1. **权限影响**：移除了SYSTEM_ALERT_WINDOW权限，FSI Activity现在作为普通Activity运行
2. **兼容性**：修复方案兼容所有Android版本
3. **功能保持**：FSI的核心功能（全屏显示、锁屏显示等）完全保持
4. **用户体验**：显著改善了用户体验，避免了意外的界面跳转

修复完成后，FSI Activity现在可以独立显示，不会再将MainActivity带回前台！
