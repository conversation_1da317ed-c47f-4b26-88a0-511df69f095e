package ai.ad.webview.fsitester;

import android.app.Activity;
import android.app.KeyguardManager;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.TextView;

/**
 * 全屏FSI Activity
 * 在FSI通知点击后显示全屏内容
 */
public class FULLWSKActivity extends Activity {
    private static final String TAG = "FULLWSKActivity";
    private static final String BAIDU_URL = "https://www.baidu.com";

    private TextView tvTitle;
    private TextView tvContent;
    private Button btnClose;
    private ImageButton ibClose;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        WSKLog.d(TAG, "onCreate -> 初始化全屏FSI Activity");

        // 设置锁屏显示和屏幕唤醒
        turnScreenOnAndKeyguardOff();

        // 配置窗口为透明（包含点击穿透）
        configureWindow(getWindow());

        // 设置窗口特性
        setupWindow();

        // 设置内容视图
        setContentView(R.layout.activity_fullwsk);

        FSIManager.getInstance().updateShowingState(true);
    }

    /**
     * 配置窗口为透明
     * 参考MainActivity的实现，并添加点击穿透功能
     *
     * @param window 窗口对象
     */
    public void configureWindow(Window window) {
        WSKLog.d(TAG, "configureWindow -> Starting window configuration");
        if (window == null) {
            WSKLog.e(TAG, "configureWindow -> Cannot configure window: window is null");
            return;
        }

        try {
            // 设置窗口背景为透明
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            window.setBackgroundDrawableResource(android.R.color.transparent);

            // 设置窗口布局参数
            WindowManager.LayoutParams params = window.getAttributes();
            params.width = WindowManager.LayoutParams.MATCH_PARENT;
            params.height = WindowManager.LayoutParams.MATCH_PARENT;

            // 设置状态栏和导航栏透明（Android 5.0及以上）
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
                window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
                window.setStatusBarColor(Color.TRANSPARENT); // 设置状态栏透明
                window.setNavigationBarColor(Color.TRANSPARENT); // 设置导航栏透明
            }

            // 设置窗口标志以允许点击穿透
            // 注意：这会让整个窗口无法接收触摸事件，需要在特定时机启用/禁用
            window.addFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);
            window.addFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL);
            WSKLog.d(TAG, "configureWindow -> 已启用点击穿透模式");

            WSKLog.d(TAG, "configureWindow -> Window configuration completed");
        } catch (Exception e) {
            WSKLog.e(TAG, "configureWindow -> Error configuring window: " + e.getMessage());
        }
    }

    /**
     * 设置窗口特性
     */
    private void setupWindow() {
        Window window = getWindow();

        // 保持屏幕常亮
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

        // 在锁屏上显示
        window.addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED
                | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON
                | WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD);

        // 修复：移除可能导致MainActivity回到前台的窗口标志
        // 不设置TYPE_APPLICATION_OVERLAY或TYPE_SYSTEM_ALERT，使用普通Activity窗口
        // 不设置FLAG_NOT_FOCUSABLE，让Activity可以正常获得焦点

        // 适配刘海屏
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            window.getAttributes().layoutInDisplayCutoutMode =
                    WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES;
        }

        // 适配沉浸式状态栏
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            window.getDecorView().setSystemUiVisibility(
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                            | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                            | View.SYSTEM_UI_FLAG_FULLSCREEN
                            | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);
        }

        WSKLog.d(TAG, "setupWindow -> 窗口特性设置完成");
    }


    /**
     * 关闭Activity
     */
    private void closeActivity() {
        WSKLog.d(TAG, "closeActivity -> 关闭全屏FSI Activity");
        finish();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // 通知FSI管理器Activity已关闭
        FSIManager.getInstance().updateShowingState(false);

        // 取消通知
        FSIManager.getInstance().closeFSI();

        WSKLog.d(TAG, "onDestroy -> 全屏FSI Activity已销毁");
    }

    /**
     * 设置屏幕唤醒和锁屏显示
     * 基于参考项目的最佳实践实现
     */
    private void turnScreenOnAndKeyguardOff() {
        WSKLog.d(TAG, "turnScreenOnAndKeyguardOff -> 设置屏幕唤醒和锁屏显示");

        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
                // Android 8.1及以上版本
                setShowWhenLocked(true);
                setTurnScreenOn(true);
                WSKLog.d(TAG, "turnScreenOnAndKeyguardOff -> 使用新API设置锁屏显示");
            } else {
                // Android 8.1以下版本
                getWindow().addFlags(
                        WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
                                | WindowManager.LayoutParams.FLAG_ALLOW_LOCK_WHILE_SCREEN_ON
                                | WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED
                                | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON);
                WSKLog.d(TAG, "turnScreenOnAndKeyguardOff -> 使用旧API设置锁屏显示");
            }

            // 请求解除锁屏
            KeyguardManager keyguardManager = (KeyguardManager) getSystemService(Context.KEYGUARD_SERVICE);
            if (keyguardManager != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                keyguardManager.requestDismissKeyguard(this, null);
                WSKLog.d(TAG, "turnScreenOnAndKeyguardOff -> 请求解除锁屏");
            }
        } catch (Exception e) {
            WSKLog.e(TAG, "turnScreenOnAndKeyguardOff -> 设置失败: " + e.getMessage());
        }
    }

    /**
     * 启用点击穿透
     * 让触摸事件穿透到下层应用
     */


    /**
     * 切换点击穿透状态
     */
}