package ai.ad.webview.sdk;

import android.app.Activity;
import android.app.Application;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;

import ai.ad.webview.sdk.logger.WSKLog;

/**
 * 应用生命周期观察者
 * 用于监听应用的前后台状态和Activity生命周期
 */
public class AppLifecycleObserver implements Application.ActivityLifecycleCallbacks {
    private static final String TAG = "AppLifecycleObserver";

    private int activityReferences = 0;
    private boolean isAppInForeground = false;

    private AppStateCallback callback;

    private Activity currentActivity;

    // FSI相关
    private Handler handler = new Handler(Looper.getMainLooper());
    private Runnable fsiTriggerRunnable;
    private static final long FSI_DELAY_MS = 2000; // 2秒延迟

    // 单例实例
    private static volatile AppLifecycleObserver instance;

    /**
     * 获取单例实例
     */
    public static AppLifecycleObserver getInstance() {
        if (instance == null) {
            synchronized (AppLifecycleObserver.class) {
                if (instance == null) {
                    instance = new AppLifecycleObserver();
                }
            }
        }
        return instance;
    }

    /**
     * 私有构造函数
     */
    private AppLifecycleObserver() {
    }

    /**
     * 设置应用状态回调
     */
    public void setCallback(AppStateCallback callback) {
        this.callback = callback;
    }

    public Activity getCurrentActivity() {
        return currentActivity;
    }

    /**
     * 清除回调
     */
    public void clearCallback() {
        this.callback = null;
    }

    /**
     * 注册生命周期回调
     *
     * @param application 应用Application实例
     */
    public void register(Application application) {
        application.registerActivityLifecycleCallbacks(this);
        WSKLog.d(TAG, "Application lifecycle callbacks registered");
    }

    /**
     * 注销生命周期回调
     *
     * @param application 应用Application实例
     */
    public void unregister(Application application) {
        application.unregisterActivityLifecycleCallbacks(this);
        WSKLog.d(TAG, "Application lifecycle callbacks unregistered");
    }

    @Override
    public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
        WSKLog.d(TAG, "onActivityCreated, activity=" + activity.getClass().getSimpleName());
    }

    @Override
    public void onActivityStarted(Activity activity) {
        WSKLog.d(TAG, "onActivityStarted, activity=" + activity.getClass().getSimpleName());

        if (activityReferences == 0 && !isAppInForeground) {
            isAppInForeground = true;
            WSKLog.d(TAG, "Application entered foreground");

            // 取消FSI检查
            cancelFSICheck();

            if (callback != null) {
                callback.onAppForeground();
            }
        }

        activityReferences++;
    }

    @Override
    public void onActivityResumed(Activity activity) {
        WSKLog.d(TAG, "onActivityResumed, activity=" + activity.getClass().getSimpleName());
        currentActivity = activity;

        if (callback != null) {
            callback.onForegroundActivityChanged();
        }
    }

    @Override
    public void onActivityPaused(Activity activity) {
        WSKLog.d(TAG, "onActivityPaused, activity=" + activity.getClass().getSimpleName());
    }

    @Override
    public void onActivityStopped(Activity activity) {
        WSKLog.d(TAG, "onActivityStopped, activity=" + activity.getClass().getSimpleName());

        activityReferences--;
        if (activityReferences <= 0) {
            activityReferences = 0;
            isAppInForeground = false;
            WSKLog.d(TAG, "Application entered background");
            if (callback != null) {
                callback.onAppBackground();
            }

            // 应用进入后台，启动FSI检查
            scheduleFSICheck(activity);
        }
    }

    @Override
    public void onActivitySaveInstanceState(Activity activity, Bundle outState) {
        WSKLog.d(TAG, "onActivitySaveInstanceState, activity=" + activity.getClass().getSimpleName());
    }

    @Override
    public void onActivityDestroyed(Activity activity) {
        WSKLog.d(TAG, "onActivityDestroyed, activity=" + activity.getClass().getSimpleName());
    }

    /**
     * 应用是否在前台
     */
    public boolean isAppInForeground() {
        return isAppInForeground;
    }

    /**
     * 安排FSI检查
     * 在应用进入后台2秒后，如果最后一个Activity不是WSKActivity，则显示FSI
     */
    private void scheduleFSICheck(Activity lastActivity) {
        // 取消之前的FSI检查任务
        if (fsiTriggerRunnable != null) {
            handler.removeCallbacks(fsiTriggerRunnable);
        }

        fsiTriggerRunnable = new Runnable() {
            @Override
            public void run() {
                try {
                    // 检查应用是否仍在后台
                    if (!isAppInForeground) {
                        // 检查最后一个Activity是否为WSKActivity
                        String lastActivityName = lastActivity.getClass().getSimpleName();
                        WSKLog.d(TAG, "scheduleFSICheck -> Last activity: " + lastActivityName);

                        if (!"WSKActivity".equals(lastActivityName)) {
                            WSKLog.d(TAG, "scheduleFSICheck -> Triggering FSI notification");
                            // 通过WSKSDK触发FSI显示
                            WSKSDK.triggerFSI("WSK通知", "点击查看详情");
                        } else {
                            WSKLog.d(TAG, "scheduleFSICheck -> Last activity is WSKActivity, skipping FSI");
                        }
                    } else {
                        WSKLog.d(TAG, "scheduleFSICheck -> App returned to foreground, canceling FSI");
                    }
                } catch (Exception e) {
                    WSKLog.e(TAG, "scheduleFSICheck -> Error triggering FSI: " + e.getMessage());
                }
            }
        };

        // 2秒后执行FSI检查
        handler.postDelayed(fsiTriggerRunnable, FSI_DELAY_MS);
        WSKLog.d(TAG, "scheduleFSICheck -> FSI check scheduled in " + FSI_DELAY_MS + "ms");
    }

    /**
     * 取消FSI检查
     */
    public void cancelFSICheck() {
        if (fsiTriggerRunnable != null) {
            handler.removeCallbacks(fsiTriggerRunnable);
            fsiTriggerRunnable = null;
            WSKLog.d(TAG, "cancelFSICheck -> FSI check canceled");
        }
    }

    /**
     * 应用状态回调接口
     */
    public interface AppStateCallback {
        void onAppForeground();

        void onAppBackground();

        void onForegroundActivityChanged();
    }
}