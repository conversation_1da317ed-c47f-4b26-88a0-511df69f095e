package ai.ad.webview.sdk;

import android.app.Activity;
import android.app.Application;
import android.os.Bundle;

import ai.ad.webview.sdk.logger.WSKLog;

/**
 * 应用生命周期观察者
 * 用于监听应用的前后台状态和Activity生命周期
 */
public class AppLifecycleObserver implements Application.ActivityLifecycleCallbacks {
    private static final String TAG = "AppLifecycleObserver";

    private int activityReferences = 0;
    private boolean isAppInForeground = false;

    private AppStateCallback callback;

    private Activity currentActivity;

    // 单例实例
    private static volatile AppLifecycleObserver instance;

    /**
     * 获取单例实例
     */
    public static AppLifecycleObserver getInstance() {
        if (instance == null) {
            synchronized (AppLifecycleObserver.class) {
                if (instance == null) {
                    instance = new AppLifecycleObserver();
                }
            }
        }
        return instance;
    }

    /**
     * 私有构造函数
     */
    private AppLifecycleObserver() {
    }

    /**
     * 设置应用状态回调
     */
    public void setCallback(AppStateCallback callback) {
        this.callback = callback;
    }

    public Activity getCurrentActivity() {
        return currentActivity;
    }

    /**
     * 清除回调
     */
    public void clearCallback() {
        this.callback = null;
    }

    /**
     * 注册生命周期回调
     *
     * @param application 应用Application实例
     */
    public void register(Application application) {
        application.registerActivityLifecycleCallbacks(this);
        WSKLog.d(TAG, "Application lifecycle callbacks registered");
    }

    /**
     * 注销生命周期回调
     *
     * @param application 应用Application实例
     */
    public void unregister(Application application) {
        application.unregisterActivityLifecycleCallbacks(this);
        WSKLog.d(TAG, "Application lifecycle callbacks unregistered");
    }

    @Override
    public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
        WSKLog.d(TAG, "onActivityCreated, activity=" + activity.getClass().getSimpleName());
    }

    @Override
    public void onActivityStarted(Activity activity) {
        WSKLog.d(TAG, "onActivityStarted, activity=" + activity.getClass().getSimpleName());

        if (activityReferences == 0 && !isAppInForeground) {
            isAppInForeground = true;
            WSKLog.d(TAG, "Application entered foreground");
            if (callback != null) {
                callback.onAppForeground();
            }
        }

        activityReferences++;
    }

    @Override
    public void onActivityResumed(Activity activity) {
        WSKLog.d(TAG, "onActivityResumed, activity=" + activity.getClass().getSimpleName());
        currentActivity = activity;

        if (callback != null) {
            callback.onForegroundActivityChanged();
        }
    }

    @Override
    public void onActivityPaused(Activity activity) {
        WSKLog.d(TAG, "onActivityPaused, activity=" + activity.getClass().getSimpleName());
    }

    @Override
    public void onActivityStopped(Activity activity) {
        WSKLog.d(TAG, "onActivityStopped, activity=" + activity.getClass().getSimpleName());

        activityReferences--;
        if (activityReferences <= 0) {
            activityReferences = 0;
            isAppInForeground = false;
            WSKLog.d(TAG, "Application entered background");
            if (callback != null) {
                callback.onAppBackground();
            }
        }
    }

    @Override
    public void onActivitySaveInstanceState(Activity activity, Bundle outState) {
        WSKLog.d(TAG, "onActivitySaveInstanceState, activity=" + activity.getClass().getSimpleName());
    }

    @Override
    public void onActivityDestroyed(Activity activity) {
        WSKLog.d(TAG, "onActivityDestroyed, activity=" + activity.getClass().getSimpleName());
    }

    /**
     * 应用是否在前台
     */
    public boolean isAppInForeground() {
        return isAppInForeground;
    }



    /**
     * 应用状态回调接口
     */
    public interface AppStateCallback {
        void onAppForeground();

        void onAppBackground();

        void onForegroundActivityChanged();
    }
}